'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FolderIcon, 
  CogIcon, 
  ListBulletIcon, 
  StarIcon,
  ChevronRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { CategoryManagement } from './category-management'
import { ServiceManagement } from './service-management'
import { ServiceOptionsManagement } from './service-options-management'
import { OptionFeaturesManagement } from './option-features-management'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  cost?: number
  discountRate?: number
  totalDiscount?: number
  isIncluded: boolean
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
    service?: {
      id: string
      name: string
      category?: {
        id: string
        name: string
      }
    }
  }
  _count?: {
    orderDetails: number
  }
}

type ActiveSection = 'categories' | 'services' | 'options' | 'features'

export function ServicesManagement() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('categories')
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [selectedOption, setSelectedOption] = useState<ServiceOption | null>(null)



  const sections = [
    {
      id: 'categories' as const,
      title: 'Categories',
      description: 'Manage service categories and subcategories',
      color: 'bg-blue-500',
      isActive: activeSection === 'categories'
    },
    {
      id: 'services' as const,
      title: 'Services',
      description: 'Manage services under categories',
      color: 'bg-green-500',
      isActive: activeSection === 'services',
      disabled: !selectedCategory
    },
    {
      id: 'options' as const,
      title: 'Service Options',
      description: 'Manage options for services',
      color: 'bg-orange-500',
      isActive: activeSection === 'options',
      disabled: !selectedService
    },
    {
      id: 'features' as const,
      title: 'Option Features',
      description: 'Manage features for service options',
      color: 'bg-purple-500',
      isActive: activeSection === 'features',
      disabled: !selectedOption
    }
  ]

  const handleSectionChange = (sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }

  const handleCategorySelect = (category: Category | null) => {
    console.log('ServicesManagement: Category selected:', category)

    // Ensure the category object is properly serialized
    const cleanCategory = category ? {
      id: String(category.id),
      name: String(category.name || ''),
      description: category.description ? String(category.description) : undefined,
      parentId: category.parentId ? String(category.parentId) : undefined,
      isActive: Boolean(category.isActive),
      displayOrder: Number(category.displayOrder || 0),
      children: category.children || [],
      _count: category._count || undefined
    } : null

    console.log('ServicesManagement: Clean category:', cleanCategory)

    setSelectedCategory(cleanCategory)
    setSelectedService(null)
    setSelectedOption(null)
    if (cleanCategory && activeSection === 'categories') {
      setActiveSection('services')
    }
  }

  const handleServiceSelect = (service: Service | null) => {
    // Ensure the service object is properly serialized
    const cleanService = service ? {
      id: String(service.id),
      categoryId: String(service.categoryId),
      name: String(service.name || ''),
      description: String(service.description || ''),
      iconClass: service.iconClass ? String(service.iconClass) : undefined,
      price: Number(service.price || 0),
      discountRate: Number(service.discountRate || 0),
      totalDiscount: Number(service.totalDiscount || 0),
      manager: service.manager ? String(service.manager) : undefined,
      isActive: Boolean(service.isActive),
      displayOrder: Number(service.displayOrder || 0),
      createdAt: service.createdAt ? String(service.createdAt) : '',
      updatedAt: service.updatedAt ? String(service.updatedAt) : '',
      category: service.category || undefined,
      _count: service._count || undefined
    } : null

    setSelectedService(cleanService)
    setSelectedOption(null)
    if (cleanService && activeSection === 'services') {
      setActiveSection('options')
    }
  }

  const handleOptionSelect = (option: ServiceOption | null) => {
    // Ensure the option object is properly serialized
    const cleanOption = option ? {
      id: String(option.id),
      serviceId: String(option.serviceId),
      name: String(option.name || ''),
      description: option.description ? String(option.description) : undefined,
      price: option.price ? Number(option.price) : undefined,
      discountRate: Number(option.discountRate || 0),
      totalDiscount: Number(option.totalDiscount || 0),
      isActive: Boolean(option.isActive),
      createdAt: option.createdAt ? String(option.createdAt) : '',
      updatedAt: option.updatedAt ? String(option.updatedAt) : '',
      service: option.service || undefined,
      features: option.features || [],
      _count: option._count || undefined
    } : null

    setSelectedOption(cleanOption)
    if (cleanOption && activeSection === 'options') {
      setActiveSection('features')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Services Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your service hierarchy: Categories → Services → Options → Features
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>Hierarchical Management System</span>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="mt-4 flex items-center space-x-2 text-sm">
          <span className="text-gray-500">Current Path:</span>
          <div className="flex items-center space-x-1">
            <span className="font-medium text-blue-600">Categories</span>
            {selectedCategory && selectedCategory.name && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-green-600">{selectedCategory.name}</span>
              </>
            )}
            {selectedService && selectedService.name && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-orange-600">{selectedService.name}</span>
              </>
            )}
            {selectedOption && selectedOption.name && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-purple-600">{selectedOption.name}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {sections.map((section) => (
          <motion.button
            key={section.id}
            onClick={() => handleSectionChange(section.id)}
            disabled={section.disabled}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              section.isActive
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : section.disabled
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
            }`}
            whileHover={!section.disabled ? { scale: 1.02 } : undefined}
            whileTap={!section.disabled ? { scale: 0.98 } : undefined}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${section.color} ${section.disabled ? 'opacity-50' : ''}`}>
                {section.id === 'categories' && <FolderIcon className="h-5 w-5 text-white" />}
                {section.id === 'services' && <CogIcon className="h-5 w-5 text-white" />}
                {section.id === 'options' && <ListBulletIcon className="h-5 w-5 text-white" />}
                {section.id === 'features' && <StarIcon className="h-5 w-5 text-white" />}
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold ${
                  section.isActive ? 'text-blue-900' : section.disabled ? 'text-gray-400' : 'text-gray-900'
                }`}>
                  {String(section.title)}
                </h3>
                <p className={`text-sm ${
                  section.isActive ? 'text-blue-600' : section.disabled ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {String(section.description)}
                </p>
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {activeSection === 'categories' && (
          <CategoryManagement
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        )}

        {activeSection === 'services' && selectedCategory && (
          <ServiceManagement
            category={selectedCategory}
            selectedService={selectedService}
            onServiceSelect={handleServiceSelect}
          />
        )}

        {activeSection === 'options' && selectedService && (
          <ServiceOptionsManagement
            service={selectedService}
            selectedOption={selectedOption}
            onOptionSelect={handleOptionSelect}
          />
        )}

        {activeSection === 'features' && selectedOption && (
          <OptionFeaturesManagement
            option={selectedOption}
          />
        )}
      </div>
    </div>
  )
}
