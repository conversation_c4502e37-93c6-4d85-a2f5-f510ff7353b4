'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  EyeIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceManagementProps {
  category: Category
  selectedService: Service | null
  onServiceSelect: (service: Service | null) => void
}

interface ServiceFormData {
  name: string
  description: string
  iconClass: string
  price: number
  discountRate: number
  totalDiscount: number
  manager: string
  isActive: boolean
  displayOrder: number
}

interface ColumnConfig {
  key: string
  label: string
  hideable?: boolean
}

export function ServiceManagement({ category, selectedService, onServiceSelect }: ServiceManagementProps) {
  console.log('ServiceManagement: Received category:', category)

  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingService, setEditingService] = useState<Service | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('grid')
  const [displayDensity, setDisplayDensity] = useState<'compact' | 'comfortable'>('comfortable')
  const [visibleColumns, setVisibleColumns] = useState<string[]>(['name', 'description', 'price', 'manager', 'isActive', 'updatedAt'])
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [filters, setFilters] = useState<Record<string, string>>({})
  
  const searchInputRef = useRef<HTMLInputElement>(null)
  
  const [formData, setFormData] = useState<ServiceFormData>({
    name: '',
    description: '',
    iconClass: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    manager: '',
    isActive: true,
    displayOrder: 0
  })

  // Column configuration
  const availableColumns: ColumnConfig[] = [
    { key: 'name', label: 'Service Name', hideable: false },
    { key: 'description', label: 'Description', hideable: true },
    { key: 'price', label: 'Price', hideable: true },
    { key: 'discountRate', label: 'Discount', hideable: true },
    { key: 'manager', label: 'Manager', hideable: true },
    { key: 'serviceOptions', label: 'Options', hideable: true },
    { key: 'orderDetails', label: 'Orders', hideable: true },
    { key: 'updatedAt', label: 'Last Updated', hideable: true },
    { key: 'isActive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    if (category && category.id) {
      fetchServices()
    }
  }, [category.id, debouncedSearchTerm, sortBy, sortOrder, filters])

  const fetchServices = async () => {
    try {
      setLoading(true)
      console.log('Frontend: Fetching services for category:', category.id, category.name)
      
      const params = new URLSearchParams({
        categoryId: category.id,
        limit: '100',
        sortBy: sortBy,
        sortOrder: sortOrder,
      })

      if (debouncedSearchTerm) {
        params.append('search', debouncedSearchTerm)
      }

      // Add filters as JSON string if any filters are set
      const activeFilters = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value && value !== '')
      )
      if (Object.keys(activeFilters).length > 0) {
        params.append('filter', JSON.stringify(activeFilters))
      }
      
      const url = `/api/admin/services?${params.toString()}`
      console.log('Frontend: API URL:', url)
      const response = await fetch(url)
      console.log('Frontend: Response status:', response.status)
      if (response.ok) {
        const data = await response.json()
        console.log('Frontend: Received services:', data.data?.length, 'services')
        console.log('Frontend: Services data:', data.data)
        setServices(data.data || [])
      } else {
        console.error('Frontend: API error:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingService 
        ? `/api/admin/services/${editingService.id}`
        : '/api/admin/services'
      
      const method = editingService ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categoryId: category.id,
          name: formData.name,
          description: formData.description,
          iconClass: formData.iconClass,
          price: formData.price,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          manager: formData.manager,
          isActive: formData.isActive,
          displayOrder: formData.displayOrder
        }),
      })

      if (response.ok) {
        await fetchServices()
        setIsFormOpen(false)
        setEditingService(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving service:', error)
    }
  }

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey: string) => {
    setVisibleColumns(prev => 
      prev.includes(columnKey)
        ? prev.filter(col => col !== columnKey)
        : [...prev, columnKey]
    )
  }

  const resetViewSettings = () => {
    setVisibleColumns(['name', 'description', 'price', 'manager', 'isActive', 'updatedAt'])
    setDisplayDensity('comfortable')
    setViewMode('grid')
    setSortBy('name')
    setSortOrder('asc')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      iconClass: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      manager: '',
      isActive: true,
      displayOrder: 0
    })
  }

  const handleEdit = (service: Service) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description,
      iconClass: service.iconClass || '',
      price: service.price,
      discountRate: service.discountRate || 0,
      totalDiscount: service.totalDiscount || 0,
      manager: service.manager || '',
      isActive: service.isActive,
      displayOrder: service.displayOrder
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (service: Service) => {
    if (!confirm(`Are you sure you want to delete "${service.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/services/${service.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchServices()
        if (selectedService?.id === service.id) {
          onServiceSelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete service')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      alert('An error occurred while deleting the service')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Services in "{category.name}"
          </h2>
          <p className="text-gray-600">Manage services under this category</p>
        </div>
        <button
          onClick={() => {
            setEditingService(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Service</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4 mb-6">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search services by name, description, manager..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchTerm !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-green-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchTerm && (
                <button
                  onClick={() => {
                    setSearchTerm('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-green-50 border-green-300 text-green-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'cards')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-green-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-green-50 text-green-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{availableColumns.filter(col => col.hideable !== false).length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-green-600 hover:text-green-800"
                        >
                          Reset
                        </button>
                      </div>

                      {availableColumns
                        .filter(col => col.hideable !== false)
                        .map((column) => {
                          const isVisible = visibleColumns.includes(column.key)
                          return (
                            <label
                              key={column.key}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={() => handleColumnToggle(column.key)}
                                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                              />
                              <span className="text-sm text-gray-700">{column.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {availableColumns.map((column) => (
                        <button
                          key={column.key}
                          onClick={() => {
                            handleSort(column.key)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === column.key
                              ? 'bg-green-50 text-green-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchTerm || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchTerm && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Search: "{searchTerm}"
                <button
                  onClick={() => setSearchTerm('')}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {availableColumns.find(col => col.key === sortBy)?.label} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {densityLabels[displayDensity]}
            </span>
          </div>
        )}
      </div>

      {/* Services Display */}
      {services.length === 0 && !loading ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <CogIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
          <p className="text-gray-500 mb-6">
            {searchTerm || Object.values(filters).some(v => v)
              ? 'Try adjusting your search or filters'
              : 'Create your first service to get started'}
          </p>
          <button
            onClick={() => {
              setEditingService(null)
              resetForm()
              setIsFormOpen(true)
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Add Service
          </button>
        </div>
      ) : viewMode === 'list' ? (
        /* Table View */
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {visibleColumns.includes('name') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-48 ${
                        displayDensity === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Service Name</span>
                        {sortBy === 'name' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('description') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-64 ${
                      displayDensity === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Description
                    </th>
                  )}
                  {visibleColumns.includes('price') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 ${
                        displayDensity === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('price')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Price</span>
                        {sortBy === 'price' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('discountRate') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24 ${
                      displayDensity === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Discount
                    </th>
                  )}
                  {visibleColumns.includes('manager') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32 ${
                      displayDensity === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Manager
                    </th>
                  )}
                  {visibleColumns.includes('serviceOptions') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20 ${
                      displayDensity === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Options
                    </th>
                  )}
                  {visibleColumns.includes('orderDetails') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20 ${
                      displayDensity === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Orders
                    </th>
                  )}
                  {visibleColumns.includes('updatedAt') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                        displayDensity === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('updatedAt')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Last Updated</span>
                        {sortBy === 'updatedAt' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('isActive') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 ${
                        displayDensity === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('isActive')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Status</span>
                        {sortBy === 'isActive' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  <th className={`px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-28 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {services.map((service) => (
                  <tr key={service.id} className={`hover:bg-gray-50 cursor-pointer ${selectedService?.id === service.id ? 'bg-green-50 border-l-4 border-green-500' : ''}`} onClick={() => onServiceSelect(service)}>
                    {visibleColumns.includes('name') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center">
                              <CogIcon className="h-5 w-5 text-green-600" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 truncate">{service.name}</div>
                          </div>
                        </div>
                      </td>
                    )}
                    {visibleColumns.includes('description') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                        <div className="text-sm text-gray-900 truncate">{service.description}</div>
                      </td>
                    )}
                    {visibleColumns.includes('price') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                        <div className="text-sm text-gray-900">${Number(service.price || 0).toFixed(2)}</div>
                      </td>
                    )}
                    {visibleColumns.includes('discountRate') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                        <div className="text-sm text-gray-500">{service.discountRate ? `${service.discountRate}%` : '-'}</div>
                      </td>
                    )}
                    {visibleColumns.includes('manager') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                        {service.manager || '-'}
                      </td>
                    )}
                    {visibleColumns.includes('serviceOptions') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900 text-center`}>
                        {service._count?.serviceOptions || 0}
                      </td>
                    )}
                    {visibleColumns.includes('orderDetails') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900 text-center`}>
                        {service._count?.orderDetails || 0}
                      </td>
                    )}
                    {visibleColumns.includes('updatedAt') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                        {formatDate(service.updatedAt)}
                      </td>
                    )}
                    {visibleColumns.includes('isActive') && (
                      <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          service.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {service.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                    )}
                    <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm font-medium text-center`}>
                      <div className="flex items-center justify-center space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEdit(service)
                          }}
                          className="text-green-600 hover:text-green-900"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDelete(service)
                          }}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        /* Grid View */
        <div className="overflow-x-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-w-[640px] md:min-w-0">
          {services.map(service => (
          services.map(service => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              onClick={() => onServiceSelect(service)}
              className={`bg-white rounded-lg shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedService?.id === service.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-green-300'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <CogIcon className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {service.name}
                    </h3>
                    <p className="text-sm text-gray-500 truncate">
                      {service.description}
                    </p>
                  </div>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  service.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {service.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500">Price:</span>
                  <span className="font-medium text-gray-900">
                    ${Number(service.price || 0).toFixed(2)}
                  </span>
                </div>
                {service.discountRate && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Discount:</span>
                    <span className="font-medium text-green-600">
                      {service.discountRate}%
                    </span>
                  </div>
                )}
                {service.manager && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Manager:</span>
                    <span className="font-medium text-gray-900">
                      {service.manager}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>{service._count?.serviceOptions || 0} Options</span>
                <span>{service._count?.orderDetails || 0} Orders</span>
                <span>{formatDate(service.updatedAt)}</span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleEdit(service)
                  }}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDelete(service)
                  }}
                  className="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          ))}
          </div>
        </div>
      ) : (
        /* Cards View */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-2">
          {services.map((service) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              onClick={() => onServiceSelect(service)}
              className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[280px] cursor-pointer ${
                selectedService?.id === service.id
                  ? 'bg-green-50 border-green-500'
                  : 'bg-white border-gray-100 hover:border-green-200'
              }`}
            >
              <div className="flex h-full">
                {/* Icon Section */}
                <div className="flex-shrink-0 w-48 relative">
                  <div className="w-full h-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                    <CogIcon className="h-16 w-16 text-white" />
                  </div>

                  {/* Status Badge Overlay */}
                  <div className="absolute top-4 right-4">
                    <span className={`inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-lg ${
                      service.isActive ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                    }`}>
                      {service.isActive ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </div>
                </div>

                {/* Content Section */}
                <div className="flex-1 p-6 flex flex-col justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                      {service.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                      {service.description}
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                      <div className="flex items-center">
                        <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                        <span>${Number(service.price || 0).toFixed(2)}</span>
                      </div>
                      {service.discountRate && (
                        <div className="flex items-center">
                          <span className="text-green-600 font-medium">{service.discountRate}% off</span>
                        </div>
                      )}
                    </div>
                    {service.manager && (
                      <p className="text-sm text-gray-600 mb-4">
                        Manager: {service.manager}
                      </p>
                    )}
                  </div>

                  <div className="space-y-3">
                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex items-center">
                        <CogIcon className="h-3 w-3 mr-1 text-green-600" />
                        <span>{service._count?.serviceOptions || 0} Options</span>
                      </div>
                      <div className="flex items-center">
                        <EyeIcon className="h-3 w-3 mr-1 text-blue-600" />
                        <span>{service._count?.orderDetails || 0} Orders</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(service)
                        }}
                        className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-green-600 text-white text-xs font-medium rounded-full hover:bg-green-700 transition-colors"
                      >
                        <PencilIcon className="h-3 w-3 mr-1" />
                        Edit
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(service)
                        }}
                        className="flex-1 inline-flex items-center justify-center px-3 py-1.5 bg-red-600 text-white text-xs font-medium rounded-full hover:bg-red-700 transition-colors"
                      >
                        <TrashIcon className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Service Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingService ? 'Edit Service' : 'Add New Service'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      rows={3}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Manager</label>
                    <input
                      type="text"
                      value={formData.manager}
                      onChange={(e) => setFormData({ ...formData, manager: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">Active</label>
                  </div>
                  <div className="flex items-center space-x-3 pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      {editingService ? 'Update' : 'Create'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsFormOpen(false)}
                      className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
