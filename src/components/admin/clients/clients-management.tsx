'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  BuildingOfficeIcon, 
  DocumentTextIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  CreditCardIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { ClientManagement } from './client-management'
import { ProjectManagement } from './project-management'
import { ContractManagement } from './contract-management'
import { InvoiceManagement } from './invoice-management'
import { PaymentManagement } from './payment-management'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  _count?: {
    projects: number
    contracts: number
    invoices: number
    payments: number
  }
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
  projStartDate?: string
  projCompletionDate?: string
  estimateCost?: number
  estimateTime?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
  createdAt: string
  updatedAt?: string
}

interface Contract {
  id: string | number
  clientId: string | number
  contName: string
  contStatus?: string
  contValue?: number
  contValueCurr?: string
  billingType?: string
  nextBillDate?: string
  contSignedDate?: string
  contExecutedDate?: string
  contExpiryDate?: string
  createdAt: string
  updatedAt?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt?: string
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  createdAt: string
  updatedAt?: string
  invoice: {
    id: string | number
    totalAmount: number
    status: string
    dueDate: string
    description?: string
  }
}

type ActiveSection = 'clients' | 'projects' | 'contracts' | 'invoices' | 'payments'

export function ClientsManagement() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('clients')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)

  const sections = [
    {
      id: 'clients' as const,
      title: 'Clients',
      description: 'Manage client information and contacts',
      color: 'bg-blue-500',
      isActive: activeSection === 'clients'
    },
    {
      id: 'projects' as const,
      title: 'Projects',
      description: 'Manage client projects and deliverables',
      color: 'bg-green-500',
      isActive: activeSection === 'projects',
      disabled: !selectedClient
    },
    {
      id: 'contracts' as const,
      title: 'Contracts',
      description: 'Manage client contracts and agreements',
      color: 'bg-orange-500',
      isActive: activeSection === 'contracts',
      disabled: !selectedClient
    },
    {
      id: 'invoices' as const,
      title: 'Invoices',
      description: 'Manage client invoices and billing',
      color: 'bg-yellow-500',
      isActive: activeSection === 'invoices',
      disabled: !selectedClient
    },
    {
      id: 'payments' as const,
      title: 'Payments',
      description: 'Manage client payments and transactions',
      color: 'bg-purple-500',
      isActive: activeSection === 'payments',
      disabled: !selectedClient
    }
  ]

  const handleSectionChange = (sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }

  const handleClientSelect = (client: Client | null) => {
    console.log('ClientsManagement: Client selected:', client)

    // Ensure the client object is properly serialized
    const cleanClient = client ? {
      id: String(client.id),
      companyName: String(client.companyName || ''),
      contactName: String(client.contactName || ''),
      contactEmail: String(client.contactEmail || ''),
      contactPhone: client.contactPhone ? String(client.contactPhone) : undefined,
      website: client.website ? String(client.website) : undefined,
      address: client.address ? String(client.address) : undefined,
      city: client.city ? String(client.city) : undefined,
      state: client.state ? String(client.state) : undefined,
      country: client.country ? String(client.country) : undefined,
      zipCode: client.zipCode ? String(client.zipCode) : undefined,
      logoUrl: client.logoUrl ? String(client.logoUrl) : undefined,
      isActive: Boolean(client.isActive),
      notes: client.notes ? String(client.notes) : undefined,
      createdAt: client.createdAt ? String(client.createdAt) : '',
      updatedAt: client.updatedAt ? String(client.updatedAt) : '',
      _count: client._count || undefined
    } : null

    console.log('ClientsManagement: Clean client:', cleanClient)

    setSelectedClient(cleanClient)
    setSelectedProject(null)
    setSelectedContract(null)
    setSelectedInvoice(null)
    if (cleanClient && activeSection === 'clients') {
      setActiveSection('projects')
    }
  }

  const handleProjectSelect = (project: Project | null) => {
    const cleanProject = project ? {
      id: String(project.id),
      clientId: String(project.clientId),
      name: String(project.name || ''),
      description: String(project.description || ''),
      status: project.status ? String(project.status) : undefined,
      projStartDate: project.projStartDate ? String(project.projStartDate) : undefined,
      projCompletionDate: project.projCompletionDate ? String(project.projCompletionDate) : undefined,
      estimateCost: project.estimateCost ? Number(project.estimateCost) : undefined,
      estimateTime: project.estimateTime ? String(project.estimateTime) : undefined,
      imageUrl: project.imageUrl ? String(project.imageUrl) : undefined,
      projectUrl: project.projectUrl ? String(project.projectUrl) : undefined,
      githubUrl: project.githubUrl ? String(project.githubUrl) : undefined,
      tags: project.tags ? String(project.tags) : undefined,
      createdAt: project.createdAt ? String(project.createdAt) : '',
      updatedAt: project.updatedAt ? String(project.updatedAt) : undefined
    } : null

    setSelectedProject(cleanProject)
  }

  const handleContractSelect = (contract: Contract | null) => {
    const cleanContract = contract ? {
      id: String(contract.id),
      clientId: String(contract.clientId),
      contName: String(contract.contName || ''),
      contStatus: contract.contStatus ? String(contract.contStatus) : undefined,
      contValue: contract.contValue ? Number(contract.contValue) : undefined,
      contValueCurr: contract.contValueCurr ? String(contract.contValueCurr) : undefined,
      billingType: contract.billingType ? String(contract.billingType) : undefined,
      nextBillDate: contract.nextBillDate ? String(contract.nextBillDate) : undefined,
      contSignedDate: contract.contSignedDate ? String(contract.contSignedDate) : undefined,
      contExecutedDate: contract.contExecutedDate ? String(contract.contExecutedDate) : undefined,
      contExpiryDate: contract.contExpiryDate ? String(contract.contExpiryDate) : undefined,
      createdAt: contract.createdAt ? String(contract.createdAt) : '',
      updatedAt: contract.updatedAt ? String(contract.updatedAt) : undefined
    } : null

    setSelectedContract(cleanContract)
  }

  const handleInvoiceSelect = (invoice: Invoice | null) => {
    const cleanInvoice = invoice ? {
      id: String(invoice.id),
      clientId: String(invoice.clientId),
      totalAmount: Number(invoice.totalAmount || 0),
      subtotal: invoice.subtotal ? Number(invoice.subtotal) : undefined,
      taxRate: Number(invoice.taxRate || 0),
      taxAmount: Number(invoice.taxAmount || 0),
      status: String(invoice.status || ''),
      dueDate: String(invoice.dueDate || ''),
      description: invoice.description ? String(invoice.description) : undefined,
      paidAt: invoice.paidAt ? String(invoice.paidAt) : undefined,
      createdAt: invoice.createdAt ? String(invoice.createdAt) : '',
      updatedAt: invoice.updatedAt ? String(invoice.updatedAt) : undefined
    } : null

    setSelectedInvoice(cleanInvoice)
    if (cleanInvoice && activeSection === 'invoices') {
      setActiveSection('payments')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Clients Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your client hierarchy: Clients → Projects → Contracts → Invoices → Payments
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>Hierarchical Management System</span>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="mt-4 flex items-center space-x-2 text-sm">
          <span className="text-gray-500">Current Path:</span>
          <div className="flex items-center space-x-1">
            <span className="font-medium text-blue-600">Clients</span>
            {selectedClient && selectedClient.companyName && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-green-600">{selectedClient.companyName}</span>
              </>
            )}
            {selectedProject && selectedProject.name && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-orange-600">{selectedProject.name}</span>
              </>
            )}
            {selectedContract && selectedContract.contName && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-yellow-600">{selectedContract.contName}</span>
              </>
            )}
            {selectedInvoice && (
              <>
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-purple-600">Invoice #{selectedInvoice.id}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {sections.map((section) => (
          <motion.button
            key={section.id}
            onClick={() => handleSectionChange(section.id)}
            disabled={section.disabled}
            className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
              section.isActive
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : section.disabled
                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
            }`}
            whileHover={!section.disabled ? { scale: 1.02 } : undefined}
            whileTap={!section.disabled ? { scale: 0.98 } : undefined}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${section.color} ${section.disabled ? 'opacity-50' : ''}`}>
                {section.id === 'clients' && <BuildingOfficeIcon className="h-5 w-5 text-white" />}
                {section.id === 'projects' && <DocumentTextIcon className="h-5 w-5 text-white" />}
                {section.id === 'contracts' && <ChartBarIcon className="h-5 w-5 text-white" />}
                {section.id === 'invoices' && <CurrencyDollarIcon className="h-5 w-5 text-white" />}
                {section.id === 'payments' && <CreditCardIcon className="h-5 w-5 text-white" />}
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold ${
                  section.isActive ? 'text-blue-900' : section.disabled ? 'text-gray-400' : 'text-gray-900'
                }`}>
                  {String(section.title)}
                </h3>
                <p className={`text-sm ${
                  section.isActive ? 'text-blue-600' : section.disabled ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {String(section.description)}
                </p>
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {activeSection === 'clients' && (
          <ClientManagement
            selectedClient={selectedClient}
            onClientSelect={handleClientSelect}
          />
        )}

        {activeSection === 'projects' && selectedClient && (
          <ProjectManagement
            client={selectedClient}
            selectedProject={selectedProject}
            onProjectSelect={handleProjectSelect}
          />
        )}

        {activeSection === 'contracts' && selectedClient && (
          <ContractManagement
            client={selectedClient}
            selectedContract={selectedContract}
            onContractSelect={handleContractSelect}
          />
        )}

        {activeSection === 'invoices' && selectedClient && (
          <InvoiceManagement
            client={selectedClient}
            selectedInvoice={selectedInvoice}
            onInvoiceSelect={handleInvoiceSelect}
          />
        )}

        {activeSection === 'payments' && selectedClient && (
          <PaymentManagement
            client={selectedClient}
            selectedInvoice={selectedInvoice}
          />
        )}
      </div>
    </div>
  )
}
