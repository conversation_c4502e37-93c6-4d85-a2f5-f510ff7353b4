'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CreditCardIcon, 
  CalendarIcon, 
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Invoice {
  id: string | number
  totalAmount: number
  status: string
  dueDate: string
  description?: string
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  createdAt: string
  updatedAt?: string
  invoice: {
    id: string | number
    totalAmount: number
    status: string
    dueDate: string
    description?: string
  }
}

interface PaymentManagementProps {
  client: Client
  selectedInvoice: Invoice | null
}

export function PaymentManagement({ client, selectedInvoice }: PaymentManagementProps) {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (client) {
      fetchPayments()
    }
  }, [client, selectedInvoice])

  const fetchPayments = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: '1',
        limit: '50',
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`/api/clients/${client.id}/payments?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch payments: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        let paymentsData = data.data.payments || []
        
        // Filter by selected invoice if one is selected
        if (selectedInvoice) {
          paymentsData = paymentsData.filter((payment: Payment) => 
            payment.invoice.id.toString() === selectedInvoice.id.toString()
          )
        }
        
        setPayments(paymentsData)
      } else {
        throw new Error(data.error || 'Failed to fetch payments')
      }
    } catch (err) {
      console.error('Error fetching payments:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setPayments([])
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchPayments()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'pending':
        return <ClockIcon className="h-4 w-4" />
      case 'failed':
        return <XCircleIcon className="h-4 w-4" />
      case 'refunded':
        return <ArrowPathIcon className="h-4 w-4" />
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method?.toLowerCase()) {
      case 'credit card':
        return <CreditCardIcon className="h-4 w-4" />
      case 'bank transfer':
      case 'wire transfer':
      case 'ach':
        return <DocumentTextIcon className="h-4 w-4" />
      default:
        return <CurrencyDollarIcon className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Payments</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchPayments}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Payments for {client.companyName}
            {selectedInvoice && (
              <span className="text-base font-normal text-gray-600">
                {' '} → Invoice #{selectedInvoice.id}
              </span>
            )}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {selectedInvoice 
              ? `Payments for invoice #${selectedInvoice.id} (${formatCurrency(selectedInvoice.totalAmount)})`
              : 'Manage payments and transactions for this client'
            }
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            href={`/clients/${client.id}/payments`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            View All Payments
          </Link>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search payments..."
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Search
          </button>
        </form>
      </div>

      {/* Payments List */}
      {payments.length === 0 ? (
        <div className="text-center py-12">
          <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm 
              ? 'Try adjusting your search terms.' 
              : selectedInvoice 
                ? 'This invoice doesn\'t have any payments yet.'
                : 'This client doesn\'t have any payments yet.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {payments.map((payment) => (
            <motion.div
              key={payment.id}
              className="border-2 border-purple-200 bg-purple-50 rounded-lg p-6 hover:shadow-lg transition-all duration-200"
              whileHover={{ scale: 1.01 }}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      {formatCurrency(payment.amount)}
                    </h3>
                    
                    {/* Status Badge */}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                      {getStatusIcon(payment.status)}
                      <span className="ml-1">{payment.status}</span>
                    </span>
                  </div>

                  {/* Payment Details */}
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center text-gray-600">
                      {getPaymentMethodIcon(payment.paymentMethod)}
                      <span className="ml-2">{payment.paymentMethod}</span>
                    </div>

                    <div className="flex items-center text-gray-600">
                      <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
                      <span>Paid {formatDate(payment.paymentDate)}</span>
                    </div>

                    <div className="flex items-center text-gray-600">
                      <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                      <span>Invoice: {formatCurrency(payment.invoice.totalAmount)}</span>
                    </div>
                  </div>

                  {/* Related Invoice Info */}
                  <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900">
                          Related Invoice #{payment.invoice.id}
                        </h4>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                          <span>Total: {formatCurrency(payment.invoice.totalAmount)}</span>
                          <span>Status: {payment.invoice.status}</span>
                          <span>Due: {formatDate(payment.invoice.dueDate)}</span>
                        </div>
                        {payment.invoice.description && (
                          <p className="mt-1 text-xs text-gray-600 line-clamp-1">
                            {payment.invoice.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Notes */}
                  {payment.notes && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">Notes</h4>
                      <p className="text-sm text-gray-600">{payment.notes}</p>
                    </div>
                  )}
                </div>

                {/* Payment Amount */}
                <div className="ml-6 text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatCurrency(payment.amount)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {payment.paymentMethod}
                  </div>
                </div>
              </div>

              {/* Payment Dates */}
              <div className="mt-6 border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div>
                    Processed {formatDate(payment.paymentDate)}
                  </div>
                  <div>
                    Recorded {formatDate(payment.createdAt)}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  )
}
