import Image from 'next/image';
import Link from 'next/link';
import { ArrowRightIcon, UsersIcon, SparklesIcon } from '@heroicons/react/24/outline';

interface Stat {
  name: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface HeroSectionProps {
  stats: Stat[];
}

export function HeroSection({ stats }: HeroSectionProps) {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Next.js Image optimization */}
      <div className="absolute inset-0">
        <Image
          src="/images/hero-bg.svg"
          alt="Technology and innovation background"
          fill
          className="object-cover"
          priority
          sizes="100vw"
          quality={85}
        />
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-purple-900/70 to-blue-800/80"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
      </div>

      {/* CSS-only floating elements for better performance */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="floating-orb floating-orb-1"></div>
        <div className="floating-orb floating-orb-2"></div>
        <div className="floating-orb floating-orb-3"></div>
        <div className="floating-orb floating-orb-4"></div>
        <div className="floating-orb floating-orb-5"></div>
      </div>

      <div className="container relative z-10 px-6 mx-auto">
        <div className="text-center max-w-6xl mx-auto">
          <div className="hero-content-animate mb-8">
            <span className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm px-6 py-3 text-sm font-medium text-white border border-white/20">
              <SparklesIcon className="w-4 h-4 mr-2 text-blue-300" />
              Crafting Digital Excellence Since 2014
            </span>
          </div>

          {/* Bold headline & sub headline */}
          <h1 className="hero-content-animate text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight text-white mb-8">
            Build the Future
            <br />
            <span className="bg-gradient-to-r from-blue-300 via-purple-300 to-blue-200 bg-clip-text text-transparent">
              Today
            </span>
          </h1>

          <p className="hero-content-animate text-lg md:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 mb-12 leading-relaxed">
            We transform innovative ideas into powerful software solutions that drive business growth.
            From startups to enterprises, we deliver cutting-edge technology with exceptional user experiences.
          </p>

          {/* CTA buttons */}
          <div className="hero-content-animate flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <Link
              href="#contact"
              className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:-translate-y-1"
            >
              Get a Quote
              <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
            <Link
              href="#team"
              className="group inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/20 hover:bg-white/20 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <UsersIcon className="mr-2 h-5 w-5" />
              Talk to an Expert
            </Link>
          </div>

          {/* Stats counters */}
          <div className="hero-content-animate grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div
                  key={stat.name}
                  className="group text-center stats-animate"
                  style={{ animationDelay: `${1 + index * 0.1}s` }}
                >
                  <div className="relative p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="inline-flex p-3 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-300 font-medium">
                      {stat.name}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
