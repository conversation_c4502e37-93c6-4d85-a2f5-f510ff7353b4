import Link from 'next/link';
import { 
  ArrowRightIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ShieldCheckIcon,
  PaintBrushIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';

interface Service {
  id: string;
  name: string;
  description?: string;
  excerpt?: string;
  category?: string;
  slug?: string;
}

interface ServicesSectionProps {
  services: Service[];
}

// Map service categories to icons
const getServiceIcon = (category: string | null | undefined) => {
  if (!category || typeof category !== 'string') {
    return CodeBracketIcon;
  }

  switch (category.toLowerCase()) {
    case 'web development':
    case 'web':
      return CodeBracketIcon;
    case 'mobile development':
    case 'mobile':
      return DevicePhoneMobileIcon;
    case 'ui/ux design':
    case 'design':
      return PaintBrushIcon;
    case 'ai/ml':
    case 'artificial intelligence':
      return BeakerIcon;
    case 'cloud':
    case 'cloud solutions':
      return CloudIcon;
    case 'security':
    case 'cybersecurity':
      return ShieldCheckIcon;
    default:
      return CodeBracketIcon;
  }
};

export function ServicesSection({ services }: ServicesSectionProps) {
  return (
    <section className="py-24 bg-gradient-to-br from-white via-blue-50/20 to-[#d0ebff]/10">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From concept to deployment, we provide end-to-end software development services
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.length > 0 ? (
            services.map((service, index) => {
              const Icon = getServiceIcon(service.category || service.name);

              return (
                <div
                  key={service.id}
                  className="service-card"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <Link
                    href={`/services/${service.slug || service.id}`}
                    className="group block h-full p-8 bg-white rounded-2xl border border-gray-200 hover:border-[#d0ebff] hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
                  >
                    <div className="flex flex-col h-full">
                      <div className="inline-flex p-4 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300 w-fit">
                        <Icon className="h-8 w-8 text-blue-700" />
                      </div>

                      <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                        {service.name}
                      </h3>

                      <p className="text-gray-600 leading-relaxed mb-6 flex-grow">
                        {service.description || service.excerpt}
                      </p>

                      <div className="flex items-center text-blue-600 font-semibold group-hover:translate-x-2 transition-transform duration-300 mt-auto">
                        Learn more
                        <ArrowRightIcon className="ml-2 h-4 w-4" />
                      </div>
                    </div>
                  </Link>
                </div>
              );
            })
          ) : (
            // Fallback content when no services are available
            Array.from({ length: 6 }, (_, index) => (
              <div
                key={index}
                className="service-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="h-full p-8 bg-white rounded-2xl border border-gray-200">
                  <div className="flex flex-col h-full">
                    <div className="inline-flex p-4 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl mb-6 w-fit">
                      <CodeBracketIcon className="h-8 w-8 text-blue-700" />
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      Service Coming Soon
                    </h3>

                    <p className="text-gray-600 leading-relaxed mb-6 flex-grow">
                      We're working on exciting new services for you. Stay tuned for updates.
                    </p>

                    <div className="flex items-center text-gray-400 font-semibold mt-auto">
                      Coming Soon
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </section>
  );
}
